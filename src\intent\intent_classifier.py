import json
import os
import re
import threading
import time
import hashlib
from pathlib import Path
from typing import Dict, Any, Tuple, List, Optional
from datetime import datetime # Added for timestamp

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from sentence_transformers import SentenceTransformer
from sklearn.preprocessing import LabelEncoder
from sklearn.utils import resample
from sklearn.model_selection import train_test_split
from sklearn.calibration import calibration_curve
from sklearn.metrics import brier_score_loss, log_loss, accuracy_score
from rapidfuzz import fuzz
from scipy.stats import entropy
from scipy.optimize import minimize_scalar

from ..utils.logger import get_logger
from ..config import DATA_DIR

import joblib
import traceback

logger = get_logger(__name__)

# Now you can use the / operator for path concatenation
INTENTS_CONFIG_PATH = DATA_DIR / "config" / "intents.json"
TRAINING_DATA_PATH = DATA_DIR / "training" / "intent_training_data.jsonl"
MODEL_DIR = DATA_DIR / "models" / "intent_classifier" # Updated as requested
MODEL_PATH = MODEL_DIR / "intent_classifier.pth"
ENCODER_PATH = MODEL_DIR / "label_encoder.joblib"
VERSION_PATH = MODEL_DIR / "version.txt"
CALIBRATION_PATH = DATA_DIR / "CALIBRATION" / "intent_calibration_stats.json" # Updated as requested

# You might also want to ensure these directories exist before using them
MODEL_DIR.mkdir(parents=True, exist_ok=True)
(DATA_DIR / "config").mkdir(parents=True, exist_ok=True)
(DATA_DIR / "training").mkdir(parents=True, exist_ok=True)
(DATA_DIR / "CALIBRATION").mkdir(parents=True, exist_ok=True)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def convert_numpy_to_python_types(obj):
    """
    Recursively converts NumPy types (np.float32, np.int64, np.bool_, np.ndarray, np.nan)
    within a dictionary or list to native Python types (float, int, bool, list, None)
    for JSON serialization.
    """
    if isinstance(obj, np.float32) or isinstance(obj, np.float64):
        return float(obj)
    elif isinstance(obj, np.int32) or isinstance(obj, np.int64):
        return int(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (float, np.number)) and np.isnan(obj):
        return None  # Convert NaN to None for JSON compatibility
    elif isinstance(obj, dict):
        return {k: convert_numpy_to_python_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_to_python_types(elem) for elem in obj]
    else:
        return obj


class AutoTemperatureCalibrator:
    """
    Performs temperature scaling for model calibration.
    """
    def __init__(self, model_output_dim: int):
        self.temperature = nn.Parameter(torch.ones(1).to(device))
        self.model_output_dim = model_output_dim

    def apply_temperature(self, logits: torch.Tensor, temperature_value: float = None) -> torch.Tensor:
        """Applies the temperature scaling to the logits."""
        if temperature_value is None:
            temperature_value = self.temperature.item()
        return logits / temperature_value

    def _loss_function(self, temperature_param: np.ndarray, logits: np.ndarray, labels: np.ndarray) -> float:
        """Loss function for temperature optimization (Negative Log Likelihood)."""
        temp_tensor = torch.tensor(temperature_param, device=device, dtype=torch.float32)
        calibrated_logits = torch.from_numpy(logits).to(device) / temp_tensor
        loss = nn.CrossEntropyLoss()(calibrated_logits, torch.from_numpy(labels).to(device))
        return loss.item()

    def calibrate(self, logits: torch.Tensor, labels: torch.Tensor) -> Tuple[float, Dict[str, Any]]:
        """
        Calibrates the model using temperature scaling on a validation set.
        Returns the optimal temperature and calibration metrics.
        """
        if logits.numel() == 0 or labels.numel() == 0:
            logger.warning("No data for calibration. Returning default temperature and empty metrics.")
            return 1.0, {
                "method": "N/A - No Data",
                "expected_calibration_error": None,
                "negative_log_likelihood": None,
                "accuracy": None,
                "data_size": 0,
                "timestamp": datetime.now().isoformat()
            }

        logits_np = logits.detach().cpu().numpy()
        labels_np = labels.detach().cpu().numpy()

        initial_temperature = 1.0

        res = minimize_scalar(
            self._loss_function,
            bounds=(0.01, 100.0),
            args=(logits_np, labels_np),
            method='bounded'
        )

        best_temperature = res.x.item()
        self.temperature.data = torch.ones(1).to(device) * best_temperature

        calibrated_probs = self.apply_temperature(logits, best_temperature).softmax(dim=1)

        ece = self._calculate_ece(calibrated_probs, labels)
        nll = self._calculate_nll(calibrated_probs, labels)
        accuracy = self._calculate_accuracy(calibrated_probs, labels)
        
        calibration_metrics = {
            "method": "temperature_scaling",
            "expected_calibration_error": ece,
            "negative_log_likelihood": nll,
            "accuracy": accuracy,
            "data_size": len(labels_np),
            "timestamp": datetime.now().isoformat()
        }

        # Safe formatting for floats or None
        def fmt(val):
            return f"{val:.4f}" if isinstance(val, (float, int)) and val is not None else "N/A"
        logger.info(f"Calibration finished. Optimal Temperature: {fmt(best_temperature)}, ECE: {fmt(ece)}, NLL: {fmt(nll)}, Accuracy: {fmt(accuracy)}")
        
        return best_temperature, calibration_metrics

    def _calculate_ece(self, calibrated_probs: torch.Tensor, labels: torch.Tensor, n_bins: int = 10) -> Optional[float]:
        """Calculates Expected Calibration Error (ECE)."""
        if labels.numel() == 0:
            return None
        
        confidences = calibrated_probs.max(dim=1).values
        predictions = calibrated_probs.argmax(dim=1)
        
        ece_sum = 0.0
        bin_boundaries = torch.linspace(0, 1, n_bins + 1).to(device)
        
        for i in range(n_bins):
            lower_bound = bin_boundaries[i]
            upper_bound = bin_boundaries[i+1]
            
            bin_indices = (confidences >= lower_bound) & (confidences < upper_bound)
            
            if bin_indices.sum() > 0:
                avg_confidence = confidences[bin_indices].mean().item()
                correct_predictions_in_bin = (predictions[bin_indices] == labels[bin_indices]).float().mean().item()
                
                ece_sum += abs(correct_predictions_in_bin - avg_confidence) * (bin_indices.sum().item() / labels.numel())
            
        return ece_sum if not np.isnan(ece_sum) else None

    def _calculate_nll(self, calibrated_probs: torch.Tensor, labels: torch.Tensor) -> Optional[float]:
        """Calculates Negative Log Likelihood (NLL)."""
        if labels.numel() == 0:
            return None
        
        try:
            # Removed 'eps' parameter as it's deprecated/removed in newer scikit-learn versions
            nll = log_loss(labels.cpu().numpy(), calibrated_probs.cpu().numpy())
            return nll if not np.isnan(nll) else None
        except ValueError as e:
            logger.warning(f"Error calculating NLL: {e}. Returning None.")
            return None

    def _calculate_accuracy(self, calibrated_probs: torch.Tensor, labels: torch.Tensor) -> Optional[float]:
        """Calculates accuracy."""
        if labels.numel() == 0:
            return None
            
        predicted_labels = torch.argmax(calibrated_probs, dim=1)
        accuracy = (predicted_labels == labels).float().mean().item()
        return accuracy if not np.isnan(accuracy) else None

class IntentClassifier(nn.Module):
    """
    A robust intent classifier built on Sentence-BERT, with auto-calibration and
    dynamic retraining capabilities. Now with production-grade enhancements.
    """
    _model_lock = threading.Lock()  # For thread safety
    _model_loaded = False
    _health_status = {'ready': False, 'last_error': None}
    _version_history = []

    def __init__(self, auto_calibrate: bool = True, retrain_on_new_data: bool = True):
        super().__init__()
        logger.info(f"Using unified model: sentence-transformers/multi-qa-mpnet-base-dot-v1")
        self.encoder = None  # Lazy load
        self.classifier_head = None
        self.label_encoder = LabelEncoder()
        self.intents: Dict[str, Any] = self._load_intents_config()
        self.auto_calibrator = None
        self.calibration_info = {'temperature': 1.0, 'calibration_metrics': {}, 'calibration_history': []}
        self._training_lock = threading.Lock()
        self.training_data = self._load_training_data()
        self._new_samples_count = 0
        self._retrain_batch_size = 10
        self.retrain_on_new_data = retrain_on_new_data
        self.auto_calibrate = auto_calibrate
        self._load_or_train_model(lazy=True)

    def _validate_query(self, query):
        if not isinstance(query, str) or not query.strip():
            raise ValueError("Query must be a non-empty string.")
        return query.strip()

    def _validate_intent(self, intent):
        if not isinstance(intent, str) or not intent.strip():
            raise ValueError("Intent must be a non-empty string.")
        return intent.strip()

    def _sanitize_path(self, path):
        # Only allow paths within DATA_DIR
        p = Path(path).resolve()
        if not str(p).startswith(str(DATA_DIR.resolve())):
            raise ValueError("Invalid path access attempt.")
        return p

    def _load_intents_config(self) -> Dict[str, Any]:
        """Loads intent configuration from JSON."""
        if not INTENTS_CONFIG_PATH.exists():
            logger.warning(f"Intents config file not found: {INTENTS_CONFIG_PATH}. Using default.")
            return {
                "leave": {"keywords": ["leave", "vacation", "time off", "sick", "absence", "pto"]},
                "benefits": {"keywords": ["benefits", "insurance", "health", "dental", "vision"]},
                "compensation": {"keywords": ["salary", "pay", "compensation", "bonus", "raise"]},
                "onboarding": {"keywords": ["onboarding", "orientation", "new hire", "training"]},
                "policy": {"keywords": ["policy", "guidelines", "rules", "procedures"]},
                "offboarding": {"keywords": ["offboarding", "exit", "termination", "resignation"]},
                "greeting": {"keywords": ["hello", "hi", "hey", "dude", "good morning", "good evening", "greetings"]},
                "general": {"keywords": ["general", "other", "miscellaneous"]},
                "irrelevant": {"keywords": []}
            }
        
        with open(INTENTS_CONFIG_PATH, "r", encoding="utf-8") as f:
            return json.load(f)

    def _load_training_data(self) -> List[Dict[str, str]]:
        """Loads training data from a JSONL file."""
        data = []
        if TRAINING_DATA_PATH.exists():
            with open(TRAINING_DATA_PATH, "r", encoding="utf-8") as f:
                for line in f:
                    data.append(json.loads(line))
        return data

    def _save_training_data(self):
        """Saves current training data to the JSONL file."""
        TRAINING_DATA_PATH.parent.mkdir(parents=True, exist_ok=True)
        with open(TRAINING_DATA_PATH, "w", encoding="utf-8") as f:
            for sample in self.training_data:
                f.write(json.dumps(sample) + "\n")

    def _load_calibration_metrics(self) -> Dict[str, Any]:
        """Load saved calibration metrics"""
        if CALIBRATION_PATH.exists():
            try:
                with open(CALIBRATION_PATH, "r", encoding="utf-8") as f:
                    return json.load(f)
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to load calibration metrics (JSONDecodeError): {e}. File might be empty or corrupt. Returning empty dict.")
                return {}
            except Exception as e:
                logger.warning(f"Failed to load calibration metrics: {e}. Returning empty dict.")
                return {}
        return {}

    def _save_calibration_metrics(self):
        """Save the current calibration temperature and metrics to file."""
        try:
            CALIBRATION_PATH.parent.mkdir(parents=True, exist_ok=True)
            serializable_info = convert_numpy_to_python_types(self.calibration_info)
            with open(CALIBRATION_PATH, "w", encoding="utf-8") as f:
                json.dump(serializable_info, f, indent=4)
            logger.info(f"Calibration metrics saved to {CALIBRATION_PATH}")
        except Exception as e:
            logger.warning(f"Failed to save calibration metrics: {e}")

    def _generate_synthetic_data(self) -> List[Dict[str, str]]:
        """Generates synthetic training data based on keywords."""
        synthetic_data = []
        for intent, config in self.intents.items():
            if "keywords" in config:
                for keyword in config["keywords"]:
                    synthetic_data.append({"query": f"I need info about {keyword}", "intent": intent})
                    synthetic_data.append({"query": f"What's the policy on {keyword}?", "intent": intent})
        return synthetic_data

    def _prepare_data(self, queries: List[str], intents: List[str]):
        """Prepares data for training: encodes labels and embeds queries."""
        if not queries or not intents:
            raise ValueError("Queries or intents list cannot be empty for data preparation.")

        # Ensure LabelEncoder is fitted on all *possible* intents (from config)
        # AND all *actual* intents present in the current batch of training data.
        unique_intents_in_data = list(set(intents))
        all_possible_and_actual_intents = sorted(list(set(list(self.intents.keys()) + unique_intents_in_data)))
        
        self.label_encoder.fit(all_possible_and_actual_intents)
        
        labels = self.label_encoder.transform(intents)
        
        embeddings = self.encoder.encode(queries, convert_to_tensor=True, device=device)
        return embeddings, torch.tensor(labels, dtype=torch.long, device=device)

    def _train_model(self):
        """Trains the intent classification model."""
        with self._training_lock:
            logger.info("Starting model training...")
            all_data = self.training_data + self._generate_synthetic_data()
            if not all_data:
                logger.warning("No training data available. Skipping model training.")
                return
            queries = [d["query"] for d in all_data]
            intents = [d["intent"] for d in all_data]
            try:
                embeddings, labels = self._prepare_data(queries, intents)
            except ValueError as e:
                logger.warning(f"Data preparation failed: {e}. Skipping training.")
                return
            if len(labels) < 2 or len(set(labels.tolist())) < 2:
                logger.warning("Not enough data or classes for stratified split. Using full data for training.")
                X_train, X_val = embeddings.cpu().numpy(), embeddings.cpu().numpy()
                y_train, y_val = labels.cpu().numpy(), labels.cpu().numpy()
            else:
                X_train, X_val, y_train, y_val = train_test_split(
                    embeddings.cpu().numpy(), labels.cpu().numpy(), test_size=0.2, random_state=42, stratify=labels.cpu().numpy()
                )
            X_train, X_val = torch.tensor(X_train, device=device), torch.tensor(X_val, device=device)
            y_train, y_val = torch.tensor(y_train, device=device), torch.tensor(y_val, device=device)
            train_dataset = TensorDataset(X_train, y_train)
            train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
            input_dim = embeddings.shape[1]
            output_dim = len(self.label_encoder.classes_)
            self.classifier_head = nn.Linear(input_dim, output_dim).to(device)
            # Ensure auto_calibrator is initialized
            self.auto_calibrator = AutoTemperatureCalibrator(output_dim)
            optimizer = torch.optim.Adam(self.classifier_head.parameters(), lr=1e-3)
            criterion = nn.CrossEntropyLoss()
            num_epochs = 10
            for epoch in range(num_epochs):
                self.classifier_head.train()
                for batch_embeddings, batch_labels in train_loader:
                    optimizer.zero_grad()
                    outputs = self.classifier_head(batch_embeddings)
                    loss = criterion(outputs, batch_labels)
                    loss.backward()
                    optimizer.step()
                logger.debug(f"Epoch {epoch+1}/{num_epochs}, Loss: {loss.item():.4f}")
            MODEL_DIR.mkdir(parents=True, exist_ok=True)
            torch.save(self.classifier_head.state_dict(), MODEL_PATH)
            logger.info(f"Model saved to {MODEL_PATH}")
            import joblib
            joblib.dump(self.label_encoder, ENCODER_PATH)
            logger.info(f"Label encoder saved to {ENCODER_PATH}")
            with open(VERSION_PATH, "w") as f:
                f.write(hashlib.md5(str(time.time()).encode()).hexdigest())
            logger.info("Model training complete.")
            self.classifier_head.eval()
            with torch.no_grad():
                val_logits = self.classifier_head(X_val)
            # Always run calibration after training
            temperature, calibration_metrics = self.auto_calibrator.calibrate(val_logits, y_val)
            self.calibration_info["temperature"] = temperature
            self.calibration_info["auto_calibrated"] = True
            self.calibration_info["calibration_metrics"] = calibration_metrics
            self.calibration_info["calibration_history"].append({
                "timestamp": datetime.now().isoformat(),
                "temperature": temperature,
                "method": calibration_metrics.get("method", "temperature_scaling"),
                "expected_calibration_error": calibration_metrics.get("expected_calibration_error"),
                "negative_log_likelihood": calibration_metrics.get("negative_log_likelihood"),
                "accuracy": calibration_metrics.get("accuracy"),
                "model_version": self._get_model_version()
            })
            self._save_calibration_metrics()

    def _load_or_train_model(self, lazy=False):
        with self._model_lock:
            try:
                if not self._model_loaded or not lazy:
                    if self.encoder is None:
                        self.encoder = SentenceTransformer("sentence-transformers/multi-qa-mpnet-base-dot-v1", device=device)
                    if not ENCODER_PATH.exists():
                        logger.warning(f"Label encoder not found at {ENCODER_PATH}. Retraining model.")
                        self._train_model()
                    else:
                        self.label_encoder = joblib.load(ENCODER_PATH)
                        input_dim = self.encoder.get_sentence_embedding_dimension()
                        output_dim = len(self.label_encoder.classes_)
                        self.classifier_head = nn.Linear(input_dim, output_dim).to(device)
                        self.classifier_head.load_state_dict(torch.load(MODEL_PATH, map_location=device))
                        self.classifier_head.eval()
                        # Ensure auto_calibrator is initialized
                        self.auto_calibrator = AutoTemperatureCalibrator(output_dim)
                        logger.info(f"Model and label encoder loaded from {MODEL_PATH} and {ENCODER_PATH}")
                        # Only perform auto-calibration if metrics are missing or outdated
                        need_calibration = True
                        if CALIBRATION_PATH.exists():
                            cal_time = CALIBRATION_PATH.stat().st_mtime
                            model_time = MODEL_PATH.stat().st_mtime if MODEL_PATH.exists() else 0
                            encoder_time = ENCODER_PATH.stat().st_mtime if ENCODER_PATH.exists() else 0
                            # If calibration is newer than both model and encoder, skip
                            if cal_time > model_time and cal_time > encoder_time:
                                logger.info("Calibration metrics are up-to-date. Skipping auto-calibration.")
                                need_calibration = False
                        if need_calibration:
                            self._perform_auto_calibration(recalibrate=True)
                self._model_loaded = True
                self._health_status['ready'] = True
                self._health_status['last_error'] = None
                self._version_history.append(self._get_model_version())
            except Exception as e:
                logger.error(f"Model loading failed: {e}\n{traceback.format_exc()}")
                self._health_status['ready'] = False
                self._health_status['last_error'] = str(e)

    def _get_model_version(self) -> str:
        """Retrieves the current model version."""
        if VERSION_PATH.exists():
            with open(VERSION_PATH, "r") as f:
                return f.read().strip()
        return "N/A"

    def _perform_auto_calibration(self, recalibrate: bool = False):
        """
        Performs or re-applies auto-calibration using temperature scaling.
        This method must be defined before it is called in __init__.
        """
        if self.classifier_head is None:
            logger.warning("Classifier head not trained/loaded, cannot perform calibration.")
            return

        if not recalibrate and self.calibration_info["auto_calibrated"]:
            logger.info("Auto-calibration already performed and not set to recalibrate. Skipping.")
            return

        logger.info("Performing auto-calibration...")

        # Prepare a small validation set for calibration if not already done during training
        # If _train_model just ran, it already used a validation set.
        # If loading an existing model, we need to generate one or use saved data.
        # For simplicity, if recalibrate is True, we'll re-run calibration on a subset of current training data.
        all_data = self.training_data + self._generate_synthetic_data()
        if not all_data:
            logger.warning("No data for calibration. Skipping auto-calibration.")
            return

        # Use a small, fresh subset for validation for calibration,
        # ensuring it's from the same distribution as training data
        queries = [d["query"] for d in all_data]
        intents = [d["intent"] for d in all_data]

        try:
            embeddings, labels = self._prepare_data(queries, intents)
        except ValueError as e:
            logger.warning(f"Data preparation for calibration failed: {e}. Skipping calibration.")
            return

        if len(labels) < 2 or len(set(labels.tolist())) < 2:
            logger.warning("Not enough data or classes for calibration split. Skipping auto-calibration.")
            return
        
        # Take a subset if the data is too large, or if not enough for split, use full data for calibration
        if len(labels) > 100: # Example: Use up to 100 samples for validation
            _, X_val_cal, _, y_val_cal = train_test_split(
                embeddings.cpu().numpy(), labels.cpu().numpy(), test_size=min(0.2, 100/len(labels)), random_state=42, stratify=labels.cpu().numpy()
            )
        else:
            X_val_cal, y_val_cal = embeddings.cpu().numpy(), labels.cpu().numpy()

        X_val_cal = torch.tensor(X_val_cal, device=device)
        y_val_cal = torch.tensor(y_val_cal, device=device)

        self.classifier_head.eval()
        with torch.no_grad():
            val_logits = self.classifier_head(X_val_cal)

        temperature, calibration_metrics = self.auto_calibrator.calibrate(val_logits, y_val_cal)

        self.calibration_info["temperature"] = temperature
        self.calibration_info["auto_calibrated"] = True
        self.calibration_info["calibration_metrics"] = calibration_metrics
        
        # Append to history, or if it's the very first calibration, this is already the current metrics
        # Avoid duplicate history entries if _train_model also triggers this.
        # This check is simplified for now.
        if not self.calibration_info["calibration_history"] or \
           self.calibration_info["calibration_history"][-1].get("timestamp") != calibration_metrics.get("timestamp"):
            self.calibration_info["calibration_history"].append({
                "timestamp": datetime.now().isoformat(), # Use current time for history
                "temperature": temperature,
                "method": calibration_metrics.get("method", "temperature_scaling"),
                "expected_calibration_error": calibration_metrics.get("expected_calibration_error"),
                "negative_log_likelihood": calibration_metrics.get("negative_log_likelihood"),
                "accuracy": calibration_metrics.get("accuracy"),
                "model_version": self._get_model_version()
            })

        self._save_calibration_metrics()
        logger.debug(f"DEBUG: Calibration metrics assigned in _perform_auto_calibration: {self.calibration_info['calibration_metrics']}")


    def update_training_data(self, query: str, intent: str):
        """Adds new training samples and triggers retraining if threshold is met."""
        self.training_data.append({"query": query, "intent": intent})
        self._save_training_data()
        self._new_samples_count += 1
        logger.info(f"Appended training data: {{'query': '{query}', 'intent': '{intent}'}}")
        
        if self.retrain_on_new_data and self._new_samples_count >= self._retrain_batch_size:
            logger.info(f"Retraining triggered due to {self._new_samples_count} new samples...")
            retrain_thread = threading.Thread(target=self._train_model)
            retrain_thread.start()
            self._new_samples_count = 0

    def get_calibration_info(self) -> Dict[str, Any]:
        """Retrieves current calibration temperature and metrics."""
        return self.calibration_info

    def classify_intent(self, query: str) -> Dict[str, Any]:
        try:
            query = self._validate_query(query)
            with self._model_lock:
                if self.classifier_head is None or not hasattr(self.label_encoder, 'classes_'):
                    self._load_or_train_model(lazy=True)
                if self.classifier_head is None or not hasattr(self.label_encoder, 'classes_'):
                    return {"intent": "unknown", "confidence": 0.0, "error": "Model not ready", "query": query}
                self.classifier_head.eval()
                query_embedding = self.encoder.encode([query], convert_to_tensor=True, device=device)
                logits = self.classifier_head(query_embedding)
                calibrated_logits = self.auto_calibrator.apply_temperature(logits, self.calibration_info["temperature"])
                probabilities = torch.softmax(calibrated_logits, dim=1)
                confidence, predicted_index = torch.max(probabilities, dim=1)
                predicted_intent = self.label_encoder.inverse_transform(predicted_index.cpu().numpy())[0]
                matched_keywords = []
                if predicted_intent in self.intents and "keywords" in self.intents[predicted_intent]:
                    for keyword in self.intents[predicted_intent]["keywords"]:
                        if fuzz.partial_ratio(keyword.lower(), query.lower()) > 80:
                            matched_keywords.append(keyword)
                if predicted_intent == "irrelevant":
                    normalized_entropy = entropy(probabilities.detach().cpu().numpy()[0]) / np.log(len(self.label_encoder.classes_))
                    confidence_score = 1.0 - normalized_entropy
                else:
                    confidence_score = confidence.item()
                return {
                    "intent": predicted_intent,
                    "confidence": confidence_score,
                    "query": query,
                    "keywords": list(set(matched_keywords)),
                    "calibration_info": self.calibration_info.copy(),
                    "error": None
                }
        except Exception as e:
            logger.error(f"Intent classification failed: {e}\n{traceback.format_exc()}")
            return {"intent": "unknown", "confidence": 0.0, "error": str(e), "query": query}

    def classify_batch(self, queries: list) -> list:
        results = []
        for q in queries:
            try:
                results.append(self.classify_intent(q))
            except Exception as e:
                results.append({"intent": "unknown", "confidence": 0.0, "error": str(e), "query": q})
        return results

    def health_check(self):
        """Returns model readiness and last error."""
        return self._health_status.copy()

    def log_resource_usage(self):
        usage = {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'available_memory_mb': psutil.virtual_memory().available // 1024 // 1024
        }
        logger.info(f"Resource usage: {usage}")
        return usage

    def get_version_history(self):
        return list(self._version_history)

    # Placeholder for monitoring hook
    def monitoring_hook(self, event, details=None):
        logger.info(f"MONITORING EVENT: {event} | Details: {details}")

    # Placeholder for access control
    def check_access(self, user):
        # Implement real access control in API layer
        return True

    # Example usage and error handling
    @staticmethod
    def example_usage():
        clf = IntentClassifier()
        print(clf.classify_intent("How do I apply for leave?"))
        print(clf.classify_batch(["How do I apply for leave?", "What is the dress code?"]))
        print(clf.health_check())
        print(clf.log_resource_usage())

if __name__ == "__main__":
    print("Initializing IntentClassifier...")
    classifier = IntentClassifier(auto_calibrate=True)
    print("\n=== Initial Classification Results with Auto-Calibration ===")

    test_queries = [
        "How do I apply for vacation leave?",
        "What is the process for requesting sick leave?",
        "Can I take unpaid time off next month?",
        "What are the benefits of our health insurance plan?",
        "Does the company offer dental coverage?",
        "How is my salary determined?",
        "What is the process for a raise?",
        "What is the company's policy on remote work?",
        "Are there guidelines for workplace safety?",
        "What is the onboarding process for new hires?",
        "How do I complete my new hire paperwork?",
        "What is the process for resigning from the company?",
        "How do I schedule an exit interview?",
        "Hello, how can I assist you today?",
        "Hi there, what can I do for you?",
        "What are the office hours?",
        "Where can I find the HR contact information?",
        "asdf jkl; qwerty zxcvb",
        "blargle fliptop monkey cheese",
        "I need to request a vacation day.",
        # Added for new intent types
        "Extract all dates from this document.",
        "Summarize the following text.",
        "Identify all named entities in this sentence.",
        "Route this query to the appropriate intent handler."
    ]

    for query in test_queries:
        result = classifier.classify_intent(query)
        print(f"Query: {query}")
        print(f"Result: {result}")
        print(f"Calibration Info for query: Temp={result.get('calibration_info', {}).get('temperature', 'N/A'):.4f}, AutoCalibrated={result.get('calibration_info', {}).get('auto_calibrated', 'N/A')}\n")

    initial_calibration_info = classifier.get_calibration_info()
    print("\n=== Calibration Information ===")
    print(f"Auto-calibrated Temperature: {initial_calibration_info['temperature']:.4f}")
    print(f"Calibration Method: {initial_calibration_info['calibration_metrics'].get('method', 'N/A')}")

    ece_value = initial_calibration_info['calibration_metrics'].get('expected_calibration_error', None)
    ece_str = f"{ece_value:.4f}" if ece_value is not None else "N/A"
    print(f"Expected Calibration Error: {ece_str}")

    nll_value = initial_calibration_info['calibration_metrics'].get('negative_log_likelihood', None)
    nll_str = f"{nll_value:.4f}" if nll_value is not None else "N/A"
    print(f"Negative Log Likelihood: {nll_str}")

    accuracy_value = initial_calibration_info['calibration_metrics'].get('accuracy', None)
    accuracy_str = f"{accuracy_value:.4f}" if accuracy_value is not None else "N/A"
    print(f"Model Accuracy: {accuracy_str}")


    print("\n=== Adding new training samples and triggering retrain with auto-recalibration ===")
    new_samples = [
        {"query": "I need info about PTO", "intent": "leave"},
        {"query": "Tell me about health insurance", "intent": "benefits"},
        {"query": "How much vacation do I get?", "intent": "leave"},
        {"query": "What's the retirement plan?", "intent": "benefits"},
        {"query": "I want to know about stock options", "intent": "compensation"}
    ]
    for sample in new_samples:
        classifier.update_training_data(sample["query"], sample["intent"])
        print(f"Added new sample: '{sample['query']}' -> '{sample['intent']}'")

    print("Waiting 10 seconds for retraining and auto-recalibration to complete...")
    time.sleep(10)

    print("\n=== Classification Results After Update and Auto-Recalibration ===")
    for query in test_queries:
        result = classifier.classify_intent(query)
        intent = result.get("intent", "unknown")
        confidence = result.get("confidence", 0.0)
        # Safe float formatting for temperature
        temp = result.get('calibration_info', {}).get('temperature', 'N/A')
        if isinstance(temp, (float, int)):
            temp_str = f"{temp:.4f}"
        else:
            temp_str = str(temp)
        auto_cal = result.get('calibration_info', {}).get('auto_calibrated', 'N/A')
        print(f"Query: {query}\n  → Predicted intent: {intent} (confidence: {confidence:.2f})\n  Calibration Temp: {temp_str}, AutoCalibrated: {auto_cal}\n")

    updated_calibration_info = classifier.get_calibration_info()
    print("\n=== Updated Calibration Information ===")
    print(f"Updated Auto-calibrated Temperature: {updated_calibration_info['temperature']:.4f}")
    print(f"Calibration Method: {updated_calibration_info['calibration_metrics'].get('method', 'N/A')}")

    ece_value = updated_calibration_info['calibration_metrics'].get('expected_calibration_error', None)
    ece_str = f"{ece_value:.4f}" if ece_value is not None else "N/A"
    print(f"Expected Calibration Error: {ece_str}")

    nll_value = updated_calibration_info['calibration_metrics'].get('negative_log_likelihood', None)
    nll_str = f"{nll_value:.4f}" if nll_value is not None else "N/A"
    print(f"Negative Log Likelihood: {nll_str}")

    accuracy_value = updated_calibration_info['calibration_metrics'].get('accuracy', None)
    accuracy_str = f"{accuracy_value:.4f}" if accuracy_value is not None else "N/A"
    print(f"Model Accuracy: {accuracy_str}")

    if updated_calibration_info['calibration_history']:
        print(f"Calibration History: {len(updated_calibration_info['calibration_history'])} calibrations performed")
        for i, cal in enumerate(updated_calibration_info['calibration_history']):
            ece_cal = cal.get('expected_calibration_error', None)
            ece_cal_str = f"{ece_cal:.4f}" if ece_cal is not None else "N/A"
            
            nll_cal = cal.get('negative_log_likelihood', None)
            nll_cal_str = f"{nll_cal:.4f}" if nll_cal is not None else "N/A"
            
            acc_cal = cal.get('accuracy', None)
            acc_cal_str = f"{acc_cal:.4f}" if acc_cal is not None else "N/A"
            
            print(f"Calibration {i+1}: Temp={cal.get('temperature', 'N/A'):.4f}, Method={cal.get('method', 'N/A')}, ECE={ece_cal_str}, NLL={nll_cal_str}, Accuracy={acc_cal_str}")
            
            
if __name__ == "__main__":
    classifier = IntentClassifier(auto_calibrate=True, retrain_on_new_data=False)
    classifier._train_model()
    print("Intent classifier training complete.")

    # --- Test queries ---
    test_queries = [
        "How do I apply for vacation leave?",
        "What is the process for requesting sick leave?",
        "Can I take unpaid time off next month?",
        "What are the benefits of our health insurance plan?",
        "Does the company offer dental coverage?",
        "How is my salary determined?",
        "What is the process for a raise?",
        "What is the company's policy on remote work?",
        "Are there guidelines for workplace safety?",
        "What is the onboarding process for new hires?",
        "How do I complete my new hire paperwork?",
        "What is the process for resigning from the company?",
        "How do I schedule an exit interview?",
        "Hello, how can I assist you today?",
        "Hi there, what can I do for you?",
        "What are the office hours?",
        "Where can I find the HR contact information?",
        "asdf jkl; qwerty zxcvb",
        "blargle fliptop monkey cheese",
        "I need to request a vacation day.",
        # Added for new intent types
        "Extract all dates from this document.",
        "Summarize the following file.",
        "Identify all named entities in this file.",
        "Route this query to the appropriate intent handler."
    ]
    print("\n--- Intent Classifier Test Results ---")
    for query in test_queries:
        result = classifier.classify_intent(query)
        intent = result.get("intent", "unknown")
        confidence = result.get("confidence", 0.0)
        # Safe float formatting for temperature
        temp = result.get('calibration_info', {}).get('temperature', 'N/A')
        if isinstance(temp, (float, int)):
            temp_str = f"{temp:.4f}"
        else:
            temp_str = str(temp)
        auto_cal = result.get('calibration_info', {}).get('auto_calibrated', 'N/A')
        print(f"Query: {query}\n  → Predicted intent: {intent} (confidence: {confidence:.2f})\n  Calibration Temp: {temp_str}, AutoCalibrated: {auto_cal}\n")