import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Message } from '@/types';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import SuggestionChips from './SuggestionChips';
import { Button } from '@/components/ui/button';
import { ArrowDown } from 'lucide-react';
import { scrollToBottom } from '@/lib/utils';

interface ChatMessagesProps {
  messages: Message[];
  isLoading: boolean;
  onSuggestionClick: (query: string) => void;
  showWelcome?: boolean;
  bottomRef?: React.RefObject<HTMLDivElement>; // NEW
}

const ChatMessages: React.FC<ChatMessagesProps> = ({
  messages,
  isLoading,
  onSuggestionClick,
  showWelcome = true,
  bottomRef // NEW
}) => {
  // const messagesEndRef = useRef<HTMLDivElement>(null); // REMOVE
  const containerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  const handleScroll = () => {
    if (!containerRef.current || !bottomRef?.current) return;

    const container = containerRef.current;
    // Check if user is at the bottom (within 1mm tolerance, approximately 3-4 pixels)
    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 4;

    // Show button only when content overflows and user is not at bottom
    const hasOverflow = container.scrollHeight > container.clientHeight;
    setShowScrollButton(!isAtBottom && hasOverflow && messages.length > 0);

    // Detect if user is manually scrolling
    if (!isAtBottom) {
      setIsUserScrolling(true);
      // Reset after a delay
      setTimeout(() => setIsUserScrolling(false), 1000);
    }
  };

  const scrollToBottomSmooth = () => {
    if (bottomRef?.current) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
      setShowScrollButton(false);
    }
  };

  useEffect(() => {
    // Only auto-scroll if user isn't manually scrolling
    if (!isUserScrolling) {
      scrollToBottomSmooth();
    }
  }, [messages, isLoading]);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Group consecutive messages from the same sender
  const groupedMessages = React.useMemo(() => {
    const groups: { messages: Message[]; sender: string; timestamp: Date }[] = [];

    messages.forEach((message) => {
      const lastGroup = groups[groups.length - 1];
      const sender = message.isUser || message.sender === 'user' ? 'user' : 'assistant';

      if (lastGroup && lastGroup.sender === sender) {
        lastGroup.messages.push(message);
      } else {
        groups.push({
          messages: [message],
          sender,
          timestamp: new Date(message.timestamp)
        });
      }
    });

    return groups;
  }, [messages]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const welcomeVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="relative flex-1 overflow-hidden">
      <div
        ref={containerRef}
        className="h-full overflow-y-auto scroll-smooth"
        onScroll={handleScroll}
      >
        <div className="max-w-4xl mx-auto px-4 py-6">
          {/* Welcome Message */}
          {false && messages.length === 0 && showWelcome && (
            <motion.div
              variants={welcomeVariants}
              initial="hidden"
              animate="visible"
              className="text-center py-12 bg-background text-foreground"
            >
              <div className="mb-8">
                <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">
                  👋 Welcome to <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">ZiaHR</span>
                </h1>
                <p className="text-lg max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
                  I can help you with questions about company policies, employee guidelines, and HR procedures.
                </p>
              </div>

              {onSuggestionClick && (
                <div className="mt-8">
                  <p className="text-sm text-muted-foreground mb-4">Try asking about:</p>
                  <SuggestionChips
                    onSuggestionClick={onSuggestionClick}
                    className="justify-center"
                  />
                </div>
              )}
            </motion.div>
          )}

          {/* Messages */}
          <AnimatePresence mode="popLayout">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-0.5"
            >
              {(() => {
                let lastUserMessage: Message | null = null;
                return groupedMessages.map((group, groupIndex) => (
                  <div key={groupIndex} className="space-y-1">
                    {group.messages.map((message, messageIndex) => {
                      let previousUserMessage: Message | null = lastUserMessage;
                      if (message.isUser || message.sender === 'user') {
                        lastUserMessage = message;
                        previousUserMessage = null;
                      } else if (lastUserMessage) {
                        previousUserMessage = lastUserMessage;
                      }
                      return (
                        <MessageBubble
                          key={message.id}
                          message={message}
                          isGrouped={messageIndex > 0}
                          showAvatar={messageIndex === 0}
                          previousUserMessage={previousUserMessage}
                        />
                      );
                    })}
                  </div>
                ));
              })()}
            </motion.div>
          </AnimatePresence>

          {/* Typing Indicator */}
          <AnimatePresence>
            {isLoading && (
              <TypingIndicator />
            )}
          </AnimatePresence>

          {/* Scroll anchor */}
          <div ref={bottomRef} className="h-4" />
        </div>
      </div>

      {/* Scroll to bottom button - positioned to be centered horizontally above input box */}
      <AnimatePresence>
        {showScrollButton && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
          >
            <Button
              variant="secondary"
              size="icon"
              onClick={scrollToBottomSmooth}
              className="rounded-full shadow-lg hover:shadow-xl transition-all bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700"
            >
              <ArrowDown className="h-4 w-4" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChatMessages;
