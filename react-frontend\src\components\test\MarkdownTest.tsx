import React from 'react';
import MessageBubble from '../chat/MessageBubble';
import { Message } from '@/types';

const MarkdownTest: React.FC = () => {
  const testMarkdownContent = `# ChatGPT-Style Markdown Rendering Test

This comprehensive test demonstrates all the enhanced Markdown features with **ChatGPT-style spacing and typography**.

## Enhanced Typography Features

The assistant messages now use the **@tailwindcss/typography** plugin combined with custom component styling for optimal readability and visual hierarchy.

### Text Formatting Showcase

Here's a paragraph with various text formatting options:

- **Bold text** for emphasis
- *Italic text* for subtle emphasis
- \`inline code\` with proper background and padding
- ~~Strikethrough text~~ for corrections (GitHub Flavored Markdown)
- [External links](https://www.google.com) that open in new tabs with proper styling

### List Improvements

#### Unordered Lists with Better Spacing:
- First item with improved vertical spacing
- Second item with **bold text** and proper indentation
- Third item with *italic text*
- Fourth item with \`inline code\` styling
  - Nested item level 1
  - Nested item level 2
    - Deeply nested item

#### Ordered Lists with Enhanced Typography:
1. First numbered item with consistent spacing
2. Second numbered item with better typography
3. Third numbered item with proper alignment
4. Fourth item demonstrating the improved numbering

### Code Block Enhancements

Here's an enhanced code block with better contrast and padding:

\`\`\`javascript
// Enhanced code block styling
function demonstrateFeatures() {
  const features = [
    'Better contrast',
    'Improved padding',
    'Horizontal scroll support',
    'Consistent font sizing'
  ];

  features.forEach(feature => {
    console.log(\`✅ \${feature}\`);
  });

  return 'ChatGPT-style code blocks!';
}

demonstrateFeatures();
\`\`\`

### Enhanced Table Styling

| Feature | Status | Improvement | Notes |
|---------|--------|-------------|-------|
| Headings | ✅ | Better spacing & hierarchy | All 6 levels supported |
| Paragraphs | ✅ | ChatGPT-style line height | Improved readability |
| Lists | ✅ | Enhanced indentation | Better nested support |
| Tables | ✅ | Improved borders & padding | Responsive design |
| Code Blocks | ✅ | Better contrast | Enhanced scrolling |
| Links | ✅ | Improved styling | New tab opening |
| Blockquotes | ✅ | Enhanced appearance | Better visual separation |
| Strikethrough | ✅ | GFM support | ~~Old text~~ → New text |

---

### Blockquote Improvements

> This is an enhanced blockquote with improved styling.
>
> It features better padding, background colors, and visual separation from the main content. The italic styling and left border create a clear distinction.
>
> Multiple paragraphs in blockquotes are now properly spaced.

### Typography Hierarchy

# H1: Main Title (2xl, bold, border-bottom)
## H2: Section Header (xl, bold, border-bottom)
### H3: Subsection (lg, semibold)
#### H4: Sub-subsection (base, semibold)
##### H5: Minor heading (sm, semibold)
###### H6: Smallest heading (xs, semibold, muted)

---

## Final Notes

This enhanced Markdown rendering provides:

1. **Better readability** with improved spacing and typography
2. **Visual hierarchy** through proper heading styles
3. **Professional appearance** matching ChatGPT's quality
4. **Dark mode support** for all elements
5. **Responsive design** that works on all devices

The implementation uses **Tailwind Typography** combined with custom React Markdown components for the best of both worlds! 🎉`;

  const testMessage: Message = {
    id: 'test-markdown',
    content: testMarkdownContent,
    isUser: false,
    timestamp: new Date(),
    sender: 'assistant'
  };

  const userTestMessage: Message = {
    id: 'test-user',
    content: 'Can you show me a comprehensive example of all the Markdown features you support? I want to see headings, lists, tables, code blocks, and other formatting options. **This text should remain as plain text** and # this should not become a heading since user messages are not processed as Markdown.',
    isUser: true,
    timestamp: new Date(),
    sender: 'user'
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <div className="max-w-5xl mx-auto p-6 space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            ChatGPT-Style Markdown Rendering Test
          </h1>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            This page demonstrates the enhanced Markdown rendering for assistant messages,
            featuring improved typography, spacing, and visual hierarchy similar to ChatGPT.
          </p>
        </div>

        <div className="space-y-8">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
              User Message (Plain Text)
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              User messages are displayed as plain text to prevent formatting issues.
            </p>
            <MessageBubble message={userTestMessage} />
          </div>

          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
              Assistant Message (Enhanced Markdown)
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Assistant messages feature full Markdown support with ChatGPT-style typography and spacing.
            </p>
            <MessageBubble message={testMessage} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkdownTest;
