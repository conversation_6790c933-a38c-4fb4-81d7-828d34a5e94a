import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Copy, Check, MoreVertical, ThumbsUp, ThumbsDown, FileText, Volume2, Share2 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Message } from '@/types';
import { Button } from '@/components/ui/button';
import { cn, formatTime, copyToClipboard, formatFileSize } from '@/lib/utils';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';

interface MessageBubbleProps {
  message: Message;
  isGrouped?: boolean;
  showAvatar?: boolean;
  previousUserMessage?: Message | null;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isGrouped = false,
  showAvatar = true,
  previousUserMessage = null
}) => {
  const [copied, setCopied] = useState(false);
  const [feedback, setFeedback] = useState<'up' | 'down' | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const isUser = message.isUser || message.sender === 'user';

  const handleCopy = async () => {
    try {
      await copyToClipboard(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleFeedback = (type: 'up' | 'down') => {
    setFeedback(feedback === type ? null : type);
    // TODO: Send feedback to backend
  };

  // Speak Out (Text-to-Speech)
  const handleSpeak = () => {
    if (!('speechSynthesis' in window)) {
      alert('Text-to-speech is not supported in this browser.');
      return;
    }
    const utterance = new window.SpeechSynthesisUtterance(message.content.replace(/<[^>]+>/g, ''));
    setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = () => setIsSpeaking(false);
    window.speechSynthesis.cancel(); // Stop any previous speech
    window.speechSynthesis.speak(utterance);
  };

  // Share Message (with dropdown)
  const shareContent = () => {
    const question = previousUserMessage ? previousUserMessage.content.replace(/<[^>]+>/g, '') : '';
    const answer = message.content.replace(/<[^>]+>/g, '');
    return question ? `Q: ${question}\nA: ${answer}` : answer;
  };

  const handleNativeShare = async () => {
    const text = shareContent();
    if (navigator.share) {
      try {
        await navigator.share({ text });
      } catch (e) {}
    } else {
      await copyToClipboard(text);
      alert('Message copied to clipboard!');
    }
  };

  const handleSlackShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://slack.com/app_redirect?channel=&message=${text}`,'_blank');
  };
  const handleEmailShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`mailto:?subject=Chatbot Q&A&body=${text}`,'_blank');
  };
  const handleLinkedInShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=&summary=${text}`,'_blank');
  };
  const handleWhatsAppShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://wa.me/?text=${text}`,'_blank');
  };
  const handleTelegramShare = () => {
    const text = encodeURIComponent(shareContent());
    window.open(`https://t.me/share/url?url=&text=${text}`,'_blank');
  };

  const renderMessageContent = (content: string) => {
    const lines = content.split('\n');
    return lines.map((line, index) => (
      <div key={index} className="mb-1 last:mb-0">
        {line}
      </div>
    ));
  };

  const renderMarkdownContent = (content: string) => {
    return (
      <div className="prose prose-gray dark:prose-invert max-w-none prose-headings:scroll-mt-20 prose-headings:font-semibold prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg prose-h4:text-base prose-h5:text-sm prose-h6:text-xs prose-p:leading-7 prose-p:mb-6 prose-li:my-1 prose-ul:my-4 prose-ol:my-4 prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-600 prose-blockquote:bg-gray-50 dark:prose-blockquote:bg-gray-800/50 prose-blockquote:py-2 prose-blockquote:px-4 prose-blockquote:my-4 prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded prose-code:text-sm prose-code:font-mono prose-code:border prose-code:border-gray-200 dark:prose-code:border-gray-700 prose-pre:bg-gray-50 dark:prose-pre:bg-gray-900 prose-pre:border prose-pre:border-gray-200 dark:prose-pre:border-gray-700 prose-pre:rounded-lg prose-pre:p-4 prose-pre:overflow-x-auto prose-table:border prose-table:border-gray-300 dark:prose-table:border-gray-600 prose-table:rounded-lg prose-thead:bg-gray-50 dark:prose-thead:bg-gray-800 prose-th:border-r prose-th:border-gray-200 dark:prose-th:border-gray-600 prose-th:px-4 prose-th:py-3 prose-th:text-left prose-th:text-xs prose-th:font-medium prose-th:text-gray-500 dark:prose-th:text-gray-400 prose-th:uppercase prose-th:tracking-wider prose-td:border-r prose-td:border-gray-200 dark:prose-td:border-gray-600 prose-td:px-4 prose-td:py-3 prose-td:text-sm prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:underline prose-a:transition-colors hover:prose-a:text-blue-800 dark:hover:prose-a:text-blue-300 prose-strong:font-semibold prose-em:italic">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={{
            // Headings with proper hierarchy and ChatGPT-style spacing
            h1: ({ children }) => (
              <h1 className="text-2xl font-bold mb-6 mt-8 first:mt-0 text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-3">
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-xl font-bold mb-5 mt-7 first:mt-0 text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-lg font-semibold mb-4 mt-6 first:mt-0 text-gray-900 dark:text-gray-100">
                {children}
              </h3>
            ),
            h4: ({ children }) => (
              <h4 className="text-base font-semibold mb-3 mt-5 first:mt-0 text-gray-900 dark:text-gray-100">
                {children}
              </h4>
            ),
            h5: ({ children }) => (
              <h5 className="text-sm font-semibold mb-2 mt-4 first:mt-0 text-gray-900 dark:text-gray-100">
                {children}
              </h5>
            ),
            h6: ({ children }) => (
              <h6 className="text-xs font-semibold mb-2 mt-3 first:mt-0 text-gray-600 dark:text-gray-400">
                {children}
              </h6>
            ),

            // Paragraphs with ChatGPT-style spacing
            p: ({ children }) => (
              <p className="mb-6 last:mb-0 text-gray-900 dark:text-gray-100 leading-7 text-[15px]">
                {children}
              </p>
            ),

          // Lists with ChatGPT-style indentation and spacing
          ul: ({ children }) => (
            <ul className="mb-6 last:mb-0 ml-6 space-y-2 list-disc text-gray-900 dark:text-gray-100 marker:text-gray-400 dark:marker:text-gray-500">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="mb-6 last:mb-0 ml-6 space-y-2 list-decimal text-gray-900 dark:text-gray-100 marker:text-gray-400 dark:marker:text-gray-500">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="leading-7 text-[15px] pl-1">
              {children}
            </li>
          ),

          // Tables with ChatGPT-style borders and padding
          table: ({ children }) => (
            <div className="mb-6 last:mb-0 overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-gray-50 dark:bg-gray-800">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600 last:border-r-0">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-6 py-4 text-[15px] text-gray-900 dark:text-gray-100 border-r border-gray-200 dark:border-gray-600 last:border-r-0 leading-6">
              {children}
            </td>
          ),

          // Code blocks with ChatGPT-style background and scroll
          code: ({ inline, children, className }: any) => {
            if (inline) {
              return (
                <code className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md text-[14px] font-mono border border-gray-200 dark:border-gray-700 font-medium">
                  {children}
                </code>
              );
            }
            return (
              <div className="mb-6 last:mb-0">
                <pre className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-5 overflow-x-auto shadow-sm">
                  <code className={`text-[14px] font-mono text-gray-900 dark:text-gray-100 leading-6 ${className || ''}`}>
                    {children}
                  </code>
                </pre>
              </div>
            );
          },

          // Horizontal rules with ChatGPT-style spacing
          hr: () => (
            <hr className="my-8 border-t border-gray-300 dark:border-gray-600" />
          ),

          // Links that open in new tab with ChatGPT-style
          a: ({ href, children }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline decoration-blue-600 dark:decoration-blue-400 underline-offset-2 transition-colors duration-150"
            >
              {children}
            </a>
          ),

          // Strong (bold) text
          strong: ({ children }) => (
            <strong className="font-semibold text-gray-900 dark:text-gray-100">
              {children}
            </strong>
          ),

          // Emphasis (italic) text
          em: ({ children }) => (
            <em className="italic text-gray-900 dark:text-gray-100">
              {children}
            </em>
          ),

          // Strikethrough text (GitHub Flavored Markdown)
          del: ({ children }) => (
            <del className="line-through text-gray-600 dark:text-gray-400">
              {children}
            </del>
          ),

          // Blockquotes with ChatGPT-style
          blockquote: ({ children }) => (
            <blockquote className="mb-6 last:mb-0 pl-6 border-l-4 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50 py-4 pr-4 rounded-r-lg text-gray-700 dark:text-gray-300 italic">
              {children}
            </blockquote>
          ),
        }}
        >
          {content}
        </ReactMarkdown>
      </div>
    );
  };

  const renderFileAttachments = () => {
    if (!message.files || message.files.length === 0) return null;

    return (
      <div className="mt-3 space-y-2">
        {message.files.map((file) => (
          <div key={file.id} className="flex items-center gap-2 rounded-lg bg-muted/50 p-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{file.name}</span>
            <span className="text-xs text-muted-foreground">
              {formatFileSize(file.size)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  const messageVariants = {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    exit: { opacity: 0, y: -20, scale: 0.95 }
  };

  return (
    <motion.div
      variants={messageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={cn(
        isUser
          ? "group flex gap-3 px-4 py-3 hover:bg-muted/30 transition-colors duration-200 flex-row-reverse"
          : "group flex gap-3 px-4 py-3 flex-row",
        !isGrouped && "mt-0"
      )}
    >
      {/* Avatar */}
      {/* Removed avatar icons for both user and bot for a cleaner look */}
      {/* Spacer for grouped messages */}
      {isGrouped && showAvatar && (
        <div className="w-8 shrink-0" />
      )}

      {/* Message Content */}
      <div className={cn(
        "flex flex-col gap-1 min-w-0 flex-1",
        isUser ? "items-end" : "items-start"
      )}>
        {/* Message Bubble */}
        <div className={cn(
          "chat-bubble relative group/bubble",
          isUser
            ? "chat-bubble-user bg-gray-200 text-gray-900 max-w-[700px] border border-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600 rounded-2xl px-4 py-1.5 shadow-md"
            : "chat-bubble-assistant max-w-[900px] text-gray-900 dark:text-gray-100 px-0 py-0 bg-transparent border-none shadow-none rounded-none hover:bg-transparent",
        )}>
          <div className="max-w-none">
            {isUser
              ? renderMessageContent(message.content)
              : renderMarkdownContent(message.content)
            }
          </div>

          {/* File Attachments */}
          {renderFileAttachments()}

          {/* Message Actions - moved to bottom, horizontal row like ChatGPT */}
          {!isUser && (
            <div className="flex flex-row gap-4 mt-5 items-center justify-start">
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={handleCopy}
                className="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150"
                title="Copy"
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={() => {
                  if (isSpeaking) {
                    window.speechSynthesis.cancel();
                    setIsSpeaking(false);
                  } else {
                    handleSpeak();
                  }
                }}
                className={`p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none transition-colors duration-150 ${isSpeaking ? 'text-[#d33] hover:text-[#a00]' : 'text-[#555] hover:text-[#222]'}`}
                title={isSpeaking ? 'Stop Speaking' : 'Speak Out'}
                aria-pressed={isSpeaking}
              >
                {isSpeaking ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="#d33"><rect x="6" y="6" width="12" height="12" rx="2" /></svg>
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={handleNativeShare}
                className="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150"
                title="Share (Native/OS)"
              >
                <Share2 className="h-4 w-4" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon-lg"
                    className="p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150"
                    title="More options"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleSlackShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/slack.svg" alt="Slack" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to Slack
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/microsoftoutlook.svg" alt="Outlook" width={16} height={16} style={{ marginRight: 10 }} />
                    Share via Outlook
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/maildotru.svg" alt="Mail" width={16} height={16} style={{ marginRight: 10 }} />
                    Share via Mail
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLinkedInShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/linkedin.svg" alt="LinkedIn" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to LinkedIn
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleWhatsAppShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/whatsapp.svg" alt="WhatsApp" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to WhatsApp
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleTelegramShare}>
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/telegram.svg" alt="Telegram" width={16} height={16} style={{ marginRight: 10 }} />
                    Share to Telegram
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={() => handleFeedback('up')}
                className={cn(
                  "p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150",
                  feedback === 'up' && "text-success-500"
                )}
                title="Like"
              >
                <ThumbsUp className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon-lg"
                onClick={() => handleFeedback('down')}
                className={cn(
                  "p-0 m-0 bg-transparent border-none shadow-none hover:bg-transparent focus:bg-transparent rounded-none text-[#555] hover:text-[#222] transition-colors duration-150",
                  feedback === 'down' && "text-destructive"
                )}
                title="Dislike"
              >
                <ThumbsDown className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Message Footer */}
        {/* Remove old message footer actions, keep only timestamp if needed */}
        <div className={cn(
          "flex items-center gap-2 px-1 mt-0 mb-0",
          isUser ? "flex-row-reverse" : "flex-row"
        )}>
          {/* Timestamp */}
          <span className="text-xs text-muted-foreground">
            {formatTime(new Date(message.timestamp))}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default MessageBubble;
