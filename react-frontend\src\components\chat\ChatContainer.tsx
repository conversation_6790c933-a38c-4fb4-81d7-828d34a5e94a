import React from 'react';
import { motion } from 'framer-motion';
import { Message, FileAttachment } from '@/types';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import HRSuggestionButtons from './HRSuggestionButtons';
import { cn } from '@/lib/utils';
import Logo from '/img/favicon.png';
import { useChat } from '@/hooks/useChat';
import { ArrowDown } from 'lucide-react';
import { useRef } from 'react';

interface ChatContainerProps {
  messages: Message[];
  isLoading: boolean;
  attachedFiles: FileAttachment[];
  onSendMessage: (message: string, files?: FileAttachment[]) => void;
  onAddFile: (file: FileAttachment) => void;
  onRemoveFile: (fileId: string) => void;
  onOpenVoice: () => void;
  onOpenEscalation: () => void;
  summarizeFile?: (file: FileAttachment) => Promise<string>;
  className?: string;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  isLoading,
  attachedFiles,
  onSendMessage,
  onAddFile,
  onRemoveFile,
  onOpenVoice,
  onOpenEscalation,
  summarizeFile,
  className,
}) => {
  // Ref to scroll to bottom
  const chatBottomRef = useRef<HTMLDivElement>(null);

  const handleScrollToBottom = () => {
    chatBottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className={cn("flex flex-col h-full bg-white dark:bg-gray-900 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-gray-100 dark:scrollbar-track-gray-800", className)}>
      {messages.length === 0 ? (
        <div className="flex-1 flex flex-col justify-center items-center h-full px-4 py-6 overflow-y-auto"> {/* py-6 for compact vertical padding */}
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-6 max-w-2xl" // mb-6 for compact margin below welcome section
          >
            <div className="mb-4"> {/* mb-4 for compact margin below logo/text */}
              <img src={Logo} alt="Company Logo" className="w-16 h-16 rounded-3xl mx-auto mb-3 shadow-md" /> {/* mb-4 for compact margin below logo */}
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1"> {/* mb-2 for compact margin below heading */}
                Welcome to ZiaHR
              </h1>
              <p className="text-base text-gray-600 dark:text-gray-300 leading-snug">
                Your intelligent HR assistant. Ask me anything about company policies, benefits, or procedures.
              </p>
            </div>
          </motion.div>

          {/* HR Suggestion Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="w-full max-w-4xl mb-8"
          >
            <HRSuggestionButtons onSuggestionClick={onSendMessage} />
          </motion.div>

          {/* Chat Input */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="w-full max-w-2xl"
          >
            <ChatInput
              onSendMessage={onSendMessage}
              attachedFiles={attachedFiles}
              onAddFile={onAddFile}
              onRemoveFile={onRemoveFile}
              onOpenVoice={onOpenVoice}
              onOpenEscalation={onOpenEscalation}
              isLoading={isLoading}
              isEmpty={true}
              onSummarizeFile={typeof summarizeFile === 'function' ? summarizeFile : undefined}
            />
          </motion.div>
        </div>
      ) : (
        <>
          <div className="flex-1 overflow-y-auto">
            <div className="max-w-3xl mx-auto px-3">
              <ChatMessages
                messages={messages}
                isLoading={isLoading}
                onSuggestionClick={onSendMessage}
                bottomRef={chatBottomRef}
              />
            </div>
          </div>
          {/* Downward Arrow Button above ChatInput */}
          <div className="w-full flex justify-center items-center mb-2">
            <button
              onClick={handleScrollToBottom}
              className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-full w-9 h-9 flex items-center justify-center shadow hover:shadow-md transition-all"
              aria-label="Scroll to bottom"
              type="button"
            >
              <ArrowDown className="h-5 w-5 text-gray-700 dark:text-gray-200" />
            </button>
          </div>
          {/* Remove border-t and border classes from this div to eliminate the horizontal line */}
          <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
            <div className="max-w-2xl mx-auto px-2 py-2">
              <ChatInput
                onSendMessage={onSendMessage}
                attachedFiles={attachedFiles}
                onAddFile={onAddFile}
                onRemoveFile={onRemoveFile}
                onOpenVoice={onOpenVoice}
                onOpenEscalation={onOpenEscalation}
                isLoading={isLoading}
                isEmpty={false}
                onSummarizeFile={typeof summarizeFile === 'function' ? summarizeFile : undefined}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatContainer;
